// pages/profile/profile.js
const authManager = require('../../utils/auth.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    favoriteRecipes: [],
    isLoading: false,
    showEditor: false,
    tempUserInfo: {
      nickName: '',
      avatarUrl: ''
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 检查是否已经登录
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 检查登录状态
  checkLoginStatus() {
    console.log('检查登录状态...');
    if (authManager.checkLoginStatus()) {
      const userInfo = authManager.getCurrentUser();
      console.log('用户已登录，用户信息:', userInfo);
      this.setData({ userInfo });
      // 获取收藏列表
      this.getFavoriteRecipes();
    } else {
      console.log('用户未登录');
      this.setData({ userInfo: null, favoriteRecipes: [] });
    }
  },

  // 登录函数 - 修复getUserProfile调用时机
  login() {
    if (this.data.isLoading) {
      return;
    }

    console.log('🚀 开始登录流程');

    // 第一步：立即获取用户信息（必须在用户直接点击事件中调用）
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: async (res) => {
        console.log('👤 获取用户信息成功:', res.userInfo);
        console.log('📝 详细信息:');
        console.log('  昵称:', res.userInfo.nickName);
        console.log('  头像:', res.userInfo.avatarUrl);
        console.log('  性别:', res.userInfo.gender);
        console.log('  国家:', res.userInfo.country);
        console.log('  省份:', res.userInfo.province);
        console.log('  城市:', res.userInfo.city);
        console.log('  语言:', res.userInfo.language);

        // 判断是否为测试数据
        if (res.userInfo.nickName === '微信用户' && res.userInfo.avatarUrl.includes('thirdwx.qlogo.cn')) {
          console.log('⚠️  注意：这是微信开发者工具的测试数据');
          console.log('   在真机上才能获取到真实的用户信息');
        } else {
          console.log('✅ 这可能是真实的用户信息');
        }

        // 开始加载状态
        this.setData({ isLoading: true });
        wx.showLoading({ title: '登录中...' });

        try {
          // 第二步：身份验证登录（传递用户信息）
          console.log('🔐 开始身份验证...');
          const loginResult = await authManager.wxLogin(res.userInfo);

          if (!loginResult.success) {
            throw new Error(loginResult.message || '身份验证失败');
          }

          console.log('✅ 身份验证成功');

          // 第三步：更新页面显示
          const userInfo = authManager.getCurrentUser();
          console.log('📱 最终用户信息:', userInfo);
          console.log('📝 最终详细信息:');
          console.log('  昵称:', userInfo.nickName);
          console.log('  头像:', userInfo.avatarUrl);

          this.setData({
            userInfo: userInfo,
            isLoading: false
          });

          // 获取收藏列表
          this.getFavoriteRecipes();

          wx.hideLoading();
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });

        } catch (error) {
          console.error('❌ 登录过程失败:', error);
          this.setData({ isLoading: false });
          wx.hideLoading();
          wx.showToast({
            title: error.message || '登录失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('❌ 用户拒绝授权:', error);
        wx.showToast({
          title: '需要授权才能登录',
          icon: 'none'
        });
      }
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          authManager.logout();
          this.setData({
            userInfo: null,
            favoriteRecipes: []
          });
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 获取收藏列表
  getFavoriteRecipes() {
    // TODO: 从服务器获取收藏列表
    // 这里使用模拟数据
    const mockFavorites = [
      {
        id: 1,
        name: '清炒时蔬',
        image: '🥬',
        cookTime: 15,
        difficulty: '简单',
        isFavorite: true
      },
      {
        id: 2,
        name: '红烧排骨',
        image: '🍖',
        cookTime: 45,
        difficulty: '中等',
        isFavorite: true
      }
    ]
    this.setData({ favoriteRecipes: mockFavorites })
  },

  // 切换收藏状态
  toggleFavorite(e) {
    const { id } = e.currentTarget.dataset
    const { favoriteRecipes } = this.data
    const index = favoriteRecipes.findIndex(item => item.id === id)
    
    if (index > -1) {
      const updatedRecipes = [...favoriteRecipes]
      updatedRecipes[index].isFavorite = !updatedRecipes[index].isFavorite
      this.setData({ favoriteRecipes: updatedRecipes })
      
      // TODO: 调用服务器API更新收藏状态
      wx.showToast({
        title: updatedRecipes[index].isFavorite ? '已收藏' : '已取消收藏',
        icon: 'success'
      })
    }
  },

  // 调试功能 - 在控制台调用
  debugUserInfo() {
    const debugTool = require('../../utils/user-info-debug.js');
    debugTool.debugUserInfo();
  },

  // 测试用户信息获取 - 在控制台调用
  testGetUserProfile() {
    const debugTool = require('../../utils/user-info-debug.js');
    debugTool.testGetUserProfile();
  },

  // 模拟真实用户信息 - 在控制台调用
  simulateRealUserInfo() {
    const debugTool = require('../../utils/user-info-debug.js');
    debugTool.simulateRealUserInfo();
    
    // 更新页面显示
    const userInfo = authManager.getCurrentUser();
    this.setData({ userInfo });
  },

  // 清除测试数据 - 在控制台调用
  clearTestData() {
    const debugTool = require('../../utils/user-info-debug.js');
    debugTool.clearTestData();
    
    // 更新页面显示
    this.setData({ userInfo: null, favoriteRecipes: [] });
  },

  // 显示个人资料编辑器
  showProfileEditor() {
    if (!this.data.userInfo) {
      // 如果用户未登录，则提示登录
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 初始化临时用户信息
    this.setData({
      showEditor: true,
      tempUserInfo: {
        nickName: this.data.userInfo.nickName,
        avatarUrl: this.data.userInfo.avatarUrl
      }
    });
  },

  // 隐藏个人资料编辑器
  hideProfileEditor() {
    this.setData({
      showEditor: false
    });
  },

  // 监听昵称输入
  onNicknameInput(e) {
    this.setData({
      'tempUserInfo.nickName': e.detail.value
    });
  },

  // 保存个人资料修改
  saveProfileChanges() {
    const { tempUserInfo } = this.data;
    
    // 验证昵称
    if (!tempUserInfo.nickName || tempUserInfo.nickName.trim() === '') {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载提示
    wx.showLoading({
      title: '保存中...'
    });
    
    // 如果头像是本地临时文件，需要先上传
    if (tempUserInfo.avatarUrl && (tempUserInfo.avatarUrl.startsWith('wxfile://') || 
        tempUserInfo.avatarUrl.startsWith('http://tmp'))) {
      // 这里应该有上传图片的逻辑，但由于我们没有后端API，先模拟一下
      setTimeout(() => {
        this.updateUserInfo(tempUserInfo);
      }, 1000);
    } else {
      // 直接更新用户信息
      this.updateUserInfo(tempUserInfo);
    }
  },

  // 更新用户信息
  updateUserInfo(newUserInfo) {
    // 更新本地存储的用户信息
    const currentUser = authManager.getCurrentUser();
    const updatedUser = {
      ...currentUser,
      nickName: newUserInfo.nickName,
      avatarUrl: newUserInfo.avatarUrl
    };
    
    // 保存到本地存储
    wx.setStorageSync('userInfo', updatedUser);
    
    // 更新authManager中的用户信息
    authManager.updateUserInfo(updatedUser);
    
    // 更新页面显示
    this.setData({
      userInfo: updatedUser,
      showEditor: false
    });
    
    wx.hideLoading();
    wx.showToast({
      title: '保存成功',
      icon: 'success'
    });
  },



  onChooseAvatar(e) {
    this.setData({
      'tempUserInfo.avatarUrl': e.detail.avatarUrl
    });
  }
})