/* pages/profile/profile.wxss */
.profile-header-spacer {
  height: 120rpx; /* Adjust this value as needed */
}

.container {
  min-height: 100vh;
  background-color: #FFF6F0;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
  padding-bottom: 50rpx;
}
.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

/* 头像容器样式 */
.avatar-container {
  position: relative;
  display: inline-block;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.edit-icon {
  position: absolute;
  right: -5px;
  bottom: -5px;
  background: #FFF;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.avatar-placeholder {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 80rpx;
  color: #ccc;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.nickname-placeholder {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.btn-login {
  margin-top: 20rpx;
  padding: 12rpx 40rpx;
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 61, 45, 0.3);
}

.btn-login[disabled] {
  background: #ccc;
  box-shadow: none;
}

.btn-logout {
  margin-top: 20rpx;
  padding: 12rpx 40rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: 1rpx solid #ddd;
}
.section { margin: 30rpx 0; }
.section-header { margin-bottom: 20rpx; }
.section-title { font-size: 32rpx; font-weight: bold; color: #333; }
.favorite-list { width: 100%; }
.favorite-item { display: flex; align-items: center; padding: 20rpx; background: #fff; border-radius: 8rpx; box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1); margin-bottom: 20rpx; }
.favorite-image { 
  width: 100rpx; 
  height: 100rpx; 
  border-radius: 8rpx; 
  margin-right: 20rpx; 
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  background-color: #f5f5f5;
}
.favorite-info { flex: 1; }
.favorite-name { font-size: 28rpx; color: #333; margin-bottom: 6rpx; display: block; }
.favorite-meta { display: flex; gap: 20rpx; font-size: 24rpx; color: #666; }
.favorite-btn { padding: 10rpx; color: #ff6b6b; font-size: 28rpx; }
.empty-state { text-align: center; padding: 40rpx; color: #999; font-size: 28rpx; }

/* 个人资料编辑器样式 */
.profile-editor {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.editor-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.editor-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  background-color: #f5f5f5;
  border-radius: 50%;
}

.avatar-editor {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.avatar-wrapper {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
}

.avatar-wrapper .avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 4rpx solid #f0f0f0;
}

.nickname-editor {
  margin-bottom: 40rpx;
}

.nickname-editor .label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.nickname-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.nickname-input:focus {
  border-color: #ff7e2d;
}

.save-btn {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  border-radius: 12rpx;
  font-size: 32rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 61, 45, 0.3);
}