/* pages/recipe/recipe.wxss */
.container {
  height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx; /* 底部留出空间 */
}

/* 顶部间距 */
.header-spacer {
  height: 120rpx;
}

/* 食材标签区域 */
.ingredients-section {
  background: #fff;
  padding: 32rpx;
  margin: 20rpx 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.03);
}

.ingredients-header {
  margin-bottom: 20rpx;
}

.ingredients-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.ingredients-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.ingredient-tag {
  background: #e8f5e8;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  border: 1rpx solid #c8e6c9;
}

.ingredient-tag.add-more {
  background: #f0f0f0;
  border: 1rpx solid #ddd;
}

.tag-text {
  font-size: 26rpx;
  color: #2e7d32;
}

.add-more .tag-text {
  color: #666;
}

/* 菜谱列表样式 */
.recipe-list {
  padding: 0 16rpx;
}

.recipe-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.03);
  overflow: hidden;
}

/* 菜谱主要信息 */
.recipe-main {
  display: flex;
  padding: 32rpx;
  gap: 24rpx;
}

.recipe-image {
  width: 120rpx;
  height: 120rpx;
  background: #ffeaa7;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.recipe-emoji {
  font-size: 48rpx;
}

.recipe-info {
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
  overflow: hidden;
}

.recipe-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recipe-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1; /* 只显示一行描述 */
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recipe-meta {
  display: flex;
  align-items: center;
  gap: 24rpx; /* 增加间距 */
  flex-wrap: nowrap;
  margin-bottom: 8rpx; /* 为星星留出空间 */
}

.meta-time, .meta-rating, .meta-views {
  display: flex;
  align-items: center;
  gap: 6rpx; /* 增加图标和文字间距 */
  flex-shrink: 0;
  white-space: nowrap;
}

.time-icon, .rating-icon, .views-icon {
  font-size: 22rpx; /* 稍微增大图标 */
  flex-shrink: 0;
}

.time-text, .rating-text, .views-text {
  font-size: 22rpx; /* 稍微增大文字 */
  color: #666;
  flex-shrink: 0;
}

/* 星星评价单独一行 */
.recipe-stars {
  margin-top: 4rpx;
}

.stars-text {
  font-size: 22rpx;
  color: #ddd;
}

/* 食材匹配度 */
.ingredient-match {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.match-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.match-ratio {
  font-size: 32rpx;
  color: #4caf50;
  font-weight: 600;
}

.match-progress {
  margin-bottom: 16rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #e0e0e0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.match-details {
  display: flex;
  justify-content: space-between;
}

.match-have {
  font-size: 24rpx;
  color: #4caf50;
}

.match-need {
  font-size: 24rpx;
  color: #ff9800;
}

/* 查看详细做法按钮 */
.recipe-action {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.detail-btn {
  width: 100%;
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 0;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 125, 45, 0.2);
  transition: all 0.3s ease;
}

.detail-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 40rpx;
  color: #666;
  font-size: 28rpx;
}

.load-more:active {
  opacity: 0.7;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}

