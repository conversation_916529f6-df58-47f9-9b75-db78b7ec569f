<!--pages/settings/settings.wxml-->
<navigation-bar title="设置" back="{{true}}" color="black" background="#FFF6F0"></navigation-bar>
<view class="container">
  <view class="settings-header-spacer"></view>
  
  <!-- 设置菜单 -->
  <view class="settings-section">
    <!-- 账户设置 -->
    <view class="section-title">账户设置</view>
    <view class="settings-group">
      <view class="setting-item" bindtap="editProfile">
        <view class="setting-item-left">
          <text class="setting-icon">👤</text>
          <text class="setting-title">编辑个人资料</text>
        </view>
        <text class="setting-arrow">></text>
      </view>
    </view>
    
    <!-- 应用设置 -->
    <view class="section-title">应用设置</view>
    <view class="settings-group">
      <view class="setting-item" bindtap="clearCache">
        <view class="setting-item-left">
          <text class="setting-icon">🗑</text>
          <text class="setting-title">清除缓存</text>
        </view>
        <text class="setting-value">{{cacheSize}}</text>
      </view>
      
      <view class="setting-item" bindtap="checkUpdate">
        <view class="setting-item-left">
          <text class="setting-icon">🔄</text>
          <text class="setting-title">检查更新</text>
        </view>
        <text class="setting-value">v1.0.0</text>
      </view>
    </view>
    
    <!-- 帮助与反馈 -->
    <view class="section-title">帮助与反馈</view>
    <view class="settings-group">
      <view class="setting-item" bindtap="showAbout">
        <view class="setting-item-left">
          <text class="setting-icon">ℹ</text>
          <text class="setting-title">关于我们</text>
        </view>
        <text class="setting-arrow">></text>
      </view>
      
      <view class="setting-item" bindtap="feedback">
        <view class="setting-item-left">
          <text class="setting-icon">💬</text>
          <text class="setting-title">意见反馈</text>
        </view>
        <text class="setting-arrow">></text>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" bindtap="logout">退出登录</button>
    </view>
  </view>
</view>
