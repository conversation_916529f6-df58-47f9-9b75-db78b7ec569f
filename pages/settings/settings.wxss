/* pages/settings/settings.wxss */
.settings-header-spacer {
  height: 120rpx;
}

.container {
  min-height: 100vh;
  background-color: #FFF6F0;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
  padding-bottom: 50rpx;
}

.settings-section {
  width: 100%;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin: 40rpx 0 20rpx 0;
  padding-left: 8rpx;
}

.section-title:first-child {
  margin-top: 0;
}

.settings-group {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #f8f8f8;
}

.setting-item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  font-size: 36rpx;
  margin-right: 24rpx;
  width: 40rpx;
  text-align: center;
}

.setting-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.setting-arrow {
  font-size: 28rpx;
  color: #999;
  font-weight: bold;
}

.setting-value {
  font-size: 28rpx;
  color: #999;
}

/* 退出登录区域 */
.logout-section {
  margin-top: 60rpx;
  padding: 0 20rpx;
}

.logout-btn {
  width: 100%;
  padding: 28rpx;
  background-color: #fff;
  color: #ff4757;
  border: 2rpx solid #ff4757;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.2s ease;
}

.logout-btn:active {
  background-color: #ff4757;
  color: #fff;
  transform: translateY(2rpx);
}
