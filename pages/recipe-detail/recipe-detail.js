// pages/recipe-detail/recipe-detail.js
const apiConfig = require('../../config/api.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    recipeId: null,
    recipeDetail: null,
    isLoading: true,
    loadError: false,
    ingredients: [],
    steps: [],
    isFavorite: false,
    // 新增数据字段
    ingredientsList: [],  // 扁平化的食材列表，用于显示状态
    stepsList: [],        // 处理后的步骤列表
    nutritionInfo: {},    // 营养信息
    userIngredients: []   // 用户拥有的食材列表
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('菜谱详情页面加载，参数:', options);
    const { id } = options;

    if (id) {
      console.log('菜谱ID:', id);
      this.setData({ recipeId: id });
      this.loadRecipeDetail(id);
    } else {
      console.error('菜谱ID为空');
      wx.showToast({
        title: '菜谱ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 加载菜谱详情
  loadRecipeDetail(recipeId) {
    this.setData({ isLoading: true, loadError: false });

    wx.showLoading({
      title: '加载中...'
    });

    const url = `${apiConfig.baseUrl}${apiConfig.api.recipeDetail}/${recipeId}`;
    console.log('请求菜谱详情URL:', url);

    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('菜谱详情响应:', res);

        if (res.statusCode === 200 && res.data && res.data.code === 0) {
          const recipeData = res.data.data;

          // 处理食材数据，按类型分组
          const ingredientGroups = this.groupIngredientsByType(recipeData.ingredients || []);

          // 处理扁平化食材列表，用于显示状态
          const ingredientsList = this.processIngredientsList(recipeData.ingredients || []);

          // 处理制作步骤
          const stepsList = this.processStepsList(recipeData.steps || []);

          // 处理营养信息
          const nutritionInfo = this.processNutritionInfo(recipeData);

          this.setData({
            recipeDetail: recipeData,
            ingredients: ingredientGroups,
            steps: recipeData.steps || [],
            ingredientsList: ingredientsList,
            stepsList: stepsList,
            nutritionInfo: nutritionInfo,
            isLoading: false,
            loadError: false
          });

          // 设置页面标题
          wx.setNavigationBarTitle({
            title: recipeData.name || '菜谱详情'
          });

          // 检查收藏状态
          this.checkFavoriteStatus(recipeId);
        } else {
          console.error('获取菜谱详情失败，状态码:', res.statusCode);
          console.error('响应数据:', res.data);
          console.log('API调用失败，使用模拟数据...');

          // API调用失败时使用模拟数据
          this.loadMockRecipeDetail(recipeId);
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('获取菜谱详情失败:', error);
        console.log('使用模拟数据...');

        // 使用模拟数据
        this.loadMockRecipeDetail(recipeId);
      }
    });
  },

  // 按类型分组食材
  groupIngredientsByType(ingredients) {
    const groups = {};

    ingredients.forEach(ingredient => {
      const type = ingredient.type || 'other';
      const typeDesc = ingredient.typeDesc || '其他';

      if (!groups[type]) {
        groups[type] = {
          type: type,
          typeDesc: typeDesc,
          items: []
        };
      }

      groups[type].items.push(ingredient);
    });

    // 转换为数组并排序（主料在前，调料在后）
    const sortOrder = { 'main': 1, 'seasoning': 2, 'other': 3 };
    return Object.values(groups).sort((a, b) => {
      return (sortOrder[a.type] || 999) - (sortOrder[b.type] || 999);
    });
  },

  // 处理扁平化食材列表，用于显示状态
  processIngredientsList(ingredients) {
    // 如果没有食材数据，使用示例数据
    if (!ingredients || ingredients.length === 0) {
      return this.getSampleIngredients();
    }

    // 获取用户拥有的食材列表（从本地存储或全局状态获取）
    const userIngredients = this.getUserIngredients();

    return ingredients.map(ingredient => {
      // 检查用户是否拥有该食材
      const hasIngredient = userIngredients.includes(ingredient.ingredientName);

      return {
        ...ingredient,
        hasIngredient: hasIngredient
      };
    });
  },

  // 获取示例食材数据
  getSampleIngredients() {
    const userIngredients = this.getUserIngredients();

    const sampleIngredients = [
      { id: 1, ingredientName: '西红柿', quantity: '适量' },
      { id: 2, ingredientName: '鸡蛋', quantity: '适量' },
      { id: 3, ingredientName: '面条', quantity: '适量' },
      { id: 4, ingredientName: '大葱', quantity: '适量' },
      { id: 5, ingredientName: '盐', quantity: '适量' },
      { id: 6, ingredientName: '糖', quantity: '适量' }
    ];

    return sampleIngredients.map(ingredient => {
      const hasIngredient = userIngredients.includes(ingredient.ingredientName);
      return {
        ...ingredient,
        hasIngredient: hasIngredient
      };
    });
  },

  // 处理制作步骤列表
  processStepsList(steps) {
    // 如果没有步骤数据，使用示例数据
    if (!steps || steps.length === 0) {
      return this.getSampleSteps();
    }

    return steps.map(step => {
      return {
        ...step,
        expanded: false // 用于控制步骤展开状态
      };
    });
  },

  // 获取示例步骤数据
  getSampleSteps() {
    return [
      {
        id: 1,
        stepOrder: 1,
        description: '准备食材：西红柿切块，鸡蛋打散，大葱切段',
        expanded: true
      },
      {
        id: 2,
        stepOrder: 2,
        description: '热锅下油，倒入蛋液炒熟盛起备用',
        expanded: true
      },
      {
        id: 3,
        stepOrder: 3,
        description: '锅内留底油，下西红柿块炒出汁水',
        expanded: true
      },
      {
        id: 4,
        stepOrder: 4,
        description: '加入适量盐和糖调味',
        expanded: true
      },
      {
        id: 5,
        stepOrder: 5,
        description: '倒入炒蛋翻炒均匀',
        expanded: true
      },
      {
        id: 6,
        stepOrder: 6,
        description: '煮面条至8分熟，倒入锅中',
        expanded: true
      },
      {
        id: 7,
        stepOrder: 7,
        description: '加入适量面汤，焖煮2分钟',
        expanded: true
      },
      {
        id: 8,
        stepOrder: 8,
        description: '撒上葱花即可出锅',
        expanded: true
      }
    ];
  },

  // 处理营养信息
  processNutritionInfo(recipeData) {
    // 如果API返回了营养信息，使用API数据，否则使用默认值
    return {
      calories: recipeData.calories || '320',
      protein: recipeData.protein || '18g',
      carbs: recipeData.carbs || '45g',
      fat: recipeData.fat || '8g'
    };
  },

  // 获取用户拥有的食材列表
  getUserIngredients() {
    // 从本地存储获取用户识别的食材
    const recognizedIngredients = wx.getStorageSync('recognizedIngredients') || [];
    return recognizedIngredients.map(item => item.name || item.ingredientName).filter(Boolean);
  },

  // 检查收藏状态
  checkFavoriteStatus(recipeId) {
    // 从本地存储获取收藏列表
    const favorites = wx.getStorageSync('favoriteRecipes') || [];
    const isFavorite = favorites.some(item => item.id == recipeId);
    this.setData({ isFavorite });
  },

  // 切换收藏状态
  toggleFavorite() {
    const { recipeDetail, isFavorite } = this.data;
    if (!recipeDetail) return;

    const favorites = wx.getStorageSync('favoriteRecipes') || [];

    if (isFavorite) {
      // 取消收藏
      const newFavorites = favorites.filter(item => item.id != recipeDetail.id);
      wx.setStorageSync('favoriteRecipes', newFavorites);
      this.setData({ isFavorite: false });

      wx.showToast({
        title: '已取消收藏',
        icon: 'success'
      });
    } else {
      // 添加收藏
      const favoriteItem = {
        id: recipeDetail.id,
        name: recipeDetail.name,
        description: recipeDetail.description,
        difficultyLevelDesc: recipeDetail.difficultyLevelDesc,
        servings: recipeDetail.servings,
        addTime: new Date().getTime()
      };

      favorites.push(favoriteItem);
      wx.setStorageSync('favoriteRecipes', favorites);
      this.setData({ isFavorite: true });

      wx.showToast({
        title: '已添加收藏',
        icon: 'success'
      });
    }
  },

  // 重新加载
  retryLoad() {
    if (this.data.recipeId) {
      this.loadRecipeDetail(this.data.recipeId);
    }
  },

  // 加载模拟菜谱详情数据
  loadMockRecipeDetail(recipeId) {
    console.log('加载模拟菜谱详情，ID:', recipeId);

    // 根据ID生成不同的模拟数据
    const mockRecipes = {
      1: {
        id: 1,
        name: '胡萝卜炒蛋',
        description: '营养丰富的家常菜，胡萝卜富含维生素A，搭配鸡蛋蛋白质丰富。',
        image: '🥕',
        cookTime: 15,
        difficulty: '简单',
        servings: 2,
        ingredients: [
          { id: 1, name: '胡萝卜', quantity: '2根', type: '蔬菜' },
          { id: 2, name: '鸡蛋', quantity: '3个', type: '蛋类' },
          { id: 3, name: '食用油', quantity: '适量', type: '调料' },
          { id: 4, name: '盐', quantity: '适量', type: '调料' },
          { id: 5, name: '葱花', quantity: '适量', type: '调料' }
        ],
        steps: [
          { id: 1, stepOrder: 1, description: '胡萝卜洗净切丝，鸡蛋打散备用', duration: 3 },
          { id: 2, stepOrder: 2, description: '热锅下油，先炒胡萝卜丝至软身', duration: 5 },
          { id: 3, stepOrder: 3, description: '倒入蛋液，快速炒匀', duration: 3 },
          { id: 4, stepOrder: 4, description: '加盐调味，撒上葱花即可', duration: 2 }
        ],
        nutrition: {
          calories: 180,
          protein: 12,
          carbs: 8,
          fat: 11
        }
      },
      2: {
        id: 2,
        name: '白菜豆腐汤',
        description: '清淡营养的汤品，白菜清热解毒，豆腐补充蛋白质。',
        image: '🥬',
        cookTime: 20,
        difficulty: '简单',
        servings: 3,
        ingredients: [
          { id: 1, name: '白菜', quantity: '300g', type: '蔬菜' },
          { id: 2, name: '豆腐', quantity: '1块', type: '豆制品' },
          { id: 3, name: '生姜', quantity: '3片', type: '调料' },
          { id: 4, name: '盐', quantity: '适量', type: '调料' },
          { id: 5, name: '香油', quantity: '几滴', type: '调料' }
        ],
        steps: [
          { id: 1, stepOrder: 1, description: '白菜洗净切段，豆腐切块', duration: 5 },
          { id: 2, stepOrder: 2, description: '锅中加水烧开，放入生姜片', duration: 3 },
          { id: 3, stepOrder: 3, description: '下豆腐块煮5分钟', duration: 5 },
          { id: 4, stepOrder: 4, description: '加入白菜段煮3分钟', duration: 3 },
          { id: 5, stepOrder: 5, description: '调味加盐，滴几滴香油即可', duration: 2 }
        ],
        nutrition: {
          calories: 120,
          protein: 8,
          carbs: 6,
          fat: 5
        }
      },
      3: {
        id: 3,
        name: '土豆丝',
        description: '经典家常菜，土豆丝爽脆可口，制作简单。',
        image: '🥔',
        cookTime: 12,
        difficulty: '简单',
        servings: 2,
        ingredients: [
          { id: 1, name: '土豆', quantity: '2个', type: '蔬菜' },
          { id: 2, name: '青椒', quantity: '1个', type: '蔬菜' },
          { id: 3, name: '蒜', quantity: '2瓣', type: '调料' },
          { id: 4, name: '醋', quantity: '1勺', type: '调料' },
          { id: 5, name: '盐', quantity: '适量', type: '调料' }
        ],
        steps: [
          { id: 1, stepOrder: 1, description: '土豆去皮切丝，用水冲洗淀粉', duration: 5 },
          { id: 2, stepOrder: 2, description: '青椒切丝，蒜切片', duration: 2 },
          { id: 3, stepOrder: 3, description: '热锅下油，爆香蒜片', duration: 1 },
          { id: 4, stepOrder: 4, description: '下土豆丝大火炒2分钟', duration: 2 },
          { id: 5, stepOrder: 5, description: '加青椒丝、醋、盐炒匀即可', duration: 2 }
        ],
        nutrition: {
          calories: 150,
          protein: 3,
          carbs: 32,
          fat: 2
        }
      }
    };

    const recipeData = mockRecipes[recipeId] || mockRecipes[1];

    // 处理食材数据
    const ingredientGroups = this.groupIngredientsByType(recipeData.ingredients);
    const ingredientsList = this.processIngredientsList(recipeData.ingredients);
    const stepsList = this.processStepsList(recipeData.steps);
    const nutritionInfo = this.processNutritionInfo(recipeData);

    this.setData({
      recipeDetail: recipeData,
      ingredients: ingredientGroups,
      steps: recipeData.steps,
      ingredientsList: ingredientsList,
      stepsList: stepsList,
      nutritionInfo: nutritionInfo,
      isLoading: false,
      loadError: false
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: recipeData.name || '菜谱详情'
    });

    wx.showToast({
      title: '已加载模拟数据',
      icon: 'success',
      duration: 1500
    });
  }
})