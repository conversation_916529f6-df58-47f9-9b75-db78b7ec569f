/* pages/recipe-detail/recipe-detail.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding-bottom: 80rpx; /* 为底部留出空间 */
}

.recipe-content {
  flex: 1;
  padding-bottom: 40rpx; /* 额外的底部间距 */
}

/* 加载状态样式 */
.loading-container, .error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-content, .error-content {
  text-align: center;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}

.retry-btn {
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  border: none;
  border-radius: 16rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 顶部间距 */
.header-spacer {
  height: 120rpx;
}

/* 菜谱主要信息卡片 */
.recipe-main-card {
  background: #fff;
  padding: 32rpx;
  margin: 20rpx 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.03);
  display: flex;
  gap: 24rpx;
  align-items: flex-start;
}

.recipe-image {
  width: 160rpx;
  height: 160rpx;
  background: #ffeaa7;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.recipe-emoji {
  font-size: 80rpx;
}

/* 菜谱信息样式 */
.recipe-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0; /* 防止flex子元素溢出 */
}

.recipe-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 16rpx;
  line-height: 1.2;
  word-wrap: break-word;
}

.recipe-meta {
  display: flex;
  gap: 20rpx;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  flex-shrink: 0;
}

.meta-icon {
  margin-right: 6rpx;
  font-size: 24rpx;
}

.meta-text {
  font-size: 24rpx;
  white-space: nowrap;
}

.recipe-stats {
  display: flex;
  gap: 20rpx;
  flex-wrap: nowrap;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  flex-shrink: 0;
  white-space: nowrap;
}

.star-icon {
  font-size: 22rpx;
  color: #ffd700;
}

.people-icon {
  font-size: 22rpx;
}

.stat-value {
  font-size: 22rpx;
  color: #666;
}

/* 区块通用样式 */
.section {
  background: #fff;
  padding: 32rpx;
  margin: 24rpx 16rpx; /* 增加上下间距 */
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.03);
  min-height: auto; /* 确保高度自适应 */
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
}

/* 食材清单样式 */
.ingredients-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  width: 100%;
  box-sizing: border-box;
}

.ingredient-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  gap: 16rpx;
}

.ingredient-item.has-ingredient {
  background: #e8f5e8;
}

.ingredient-item.missing-ingredient {
  background: #ffeaea;
}

.ingredient-status {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.status-icon {
  font-size: 24rpx;
  font-weight: 600;
}

.status-icon.has {
  color: #4caf50;
  background: #e8f5e8;
  border: 2rpx solid #4caf50;
  border-radius: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-icon.missing {
  color: #f44336;
  background: #ffeaea;
  border: 2rpx solid #f44336;
  border-radius: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ingredient-info {
  flex: 1;
}

.ingredient-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.ingredient-quantity {
  display: flex;
  align-items: center;
}

.quantity-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 营养信息样式 */
.nutrition-container {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.nutrition-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.nutrition-value {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.nutrition-value.calories {
  color: #ff6b35;
}

.nutrition-value.protein {
  color: #4a90e2;
}

.nutrition-value.carbs {
  color: #4caf50;
}

.nutrition-value.fat {
  color: #9c27b0;
}

.nutrition-label {
  font-size: 24rpx;
  color: #666;
}

/* 制作步骤样式 */
.steps-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  width: 100%;
  box-sizing: border-box;
  padding-bottom: 20rpx; /* 确保最后一项有足够空间 */
}

.step-item {
  display: flex;
  gap: 20rpx;
  align-items: flex-start;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.step-number {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  border-radius: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  flex: 1;
}

.step-expand {
  width: 48rpx;
  height: 48rpx;
  background: #fff;
  border-radius: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin-left: 16rpx;
}

.expand-icon {
  font-size: 24rpx;
  color: #666;
  font-weight: 600;
}

/* 底部间距 */
.bottom-spacing {
  height: 100rpx;
}

