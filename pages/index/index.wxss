/**index.wxss**/
page, .container {
  background: #FFF6F0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx; /* 为底部tabbar留出空间 */
}
.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

/* 搜索栏样式 */
.search-bar {
  padding: 20rpx 30rpx;
  background: #FFFFFF;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-input {
  display: flex;
  align-items: center;
  background: var(--background-color);
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-placeholder {
  color: var(--text-color-secondary);
  font-size: 28rpx;
}

/* 拍照入口样式 */
.camera-entry {
  margin: 30rpx;
  background: linear-gradient(135deg, var(--primary-color), #81C784);
}

.camera-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
}

.camera-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
}

.camera-text {
  flex: 1;
}

.camera-title {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.camera-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
}

/* 区块通用样式 */
.section {
  margin: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
}

.section-more {
  font-size: 24rpx;
  color: var(--text-color-secondary);
}

/* 菜谱卡片样式 */
.recipe-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.recipe-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.recipe-image {
  width: 100%;
  height: 320rpx;
  background-color: #F5F5F5;
}

.recipe-info {
  padding: 16rpx;
}

.recipe-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 8rpx;
  display: block;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: var(--text-color-secondary);
}

/* 食材分类样式 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 12rpx;
}

.category-name {
  font-size: 24rpx;
  color: var(--text-color);
}

/* 动画效果 */
.recipe-card {
  transition: transform 0.3s ease;
}

.recipe-card:active {
  transform: scale(0.98);
}

.camera-entry {
  transition: transform 0.3s ease;
}

.camera-entry:active {
  transform: scale(0.98);
}

.camera-entry.large {
  margin: 40rpx 0 60rpx 0;
  min-height: 320rpx;
  background: linear-gradient(135deg, var(--primary-color), #81C784);
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
}

.camera-content.large {
  padding: 60rpx 40rpx;
  align-items: center;
}

.camera-icon.large {
  width: 160rpx;
  height: 160rpx;
  font-size: 120rpx;
  margin-right: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 拍照识别卡片 */
.camera-card {
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255,107,107,0.08);
  padding: 60rpx 40rpx 40rpx 40rpx;
  margin: 40rpx 20rpx 32rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.camera-icon-bg {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ff9a6b);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
}
.camera-icon-large {
  font-size: 96rpx;
  color: #fff;
}
.camera-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 12rpx;
  margin-top: 0;
}
.camera-desc {
  font-size: 26rpx;
  color: #888;
  margin-bottom: 36rpx;
}
.camera-btn {
  width: 80%;
  height: 88rpx;
  background: linear-gradient(90deg, #ff6b6b, #ff9a6b);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  border-radius: 44rpx;
  margin-top: 0;
  margin-bottom: 0;
  box-shadow: 0 4rpx 16rpx rgba(255,107,107,0.12);
}

/* 功能区块 */
.feature-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 0 20rpx 40rpx 20rpx;
}
.feature-card {
  flex: 1;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.04);
  margin: 0 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 36rpx 0 28rpx 0;
}
.feature-icon {
  font-size: 48rpx;
  color: #ff6b6b;
  margin-bottom: 12rpx;
}
.feature-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 6rpx;
}
.feature-desc {
  font-size: 22rpx;
  color: #888;
}

/* 推荐列表 */
.recipe-list {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
}
.recipe-item {
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.04);
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 24rpx 20rpx;
}
.recipe-img-area {
  width: 96rpx;
  height: 96rpx;
  border-radius: 18rpx;
  background: #fff3f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 28rpx;
}
.recipe-img {
  font-size: 56rpx;
}
.recipe-info-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.recipe-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #222;
  margin-bottom: 10rpx;
}
.recipe-meta {
  display: flex;
  flex-direction: row;
  gap: 24rpx;
  font-size: 24rpx;
  color: #ffb84d;
}
.meta-time {
  color: #888;
  font-size: 22rpx;
}
.meta-star {
  color: #ffb84d;
  font-size: 22rpx;
}
